@import url("https://fonts.googleapis.com/css?family=Poppins:wght@300;400; 500;600&display=swap");

:root {
    --primary-color-hue: 252;
    --dark-color-lightness: 17%;
    --light-color-lightness: 95%;
    --white-color-lightness: 100%;

    --color-white: hsl(252, 30%, var(--white-color-lightness));
    --color-light: hsl(252, 30%, var(--light-color-lightness));
    --color-gray: hsl(var(--primary-color-hue), 15%, 65%);
    --color-primary: hsl(var(--primary-color-hue), 75%, 60%);
    --color-secondary: hsl(var(--primary-color-hue), 100%, 90%);
    --color-success: hsl(120, 95%, 65%);
    --color-danger: hsl(0, 95%, 65%);
    --color-dark: hsl(var(--primary-color-hue), 30%, var(--dark-color-lightness));
    --color-black: hsl(var(--primary-color-hue), 30%, 10%);

    --border-radius: 2rem;
    --card-border-radius: 1rem;
    --btn-padding: 0.6rem 2rem;
    --search-padding: 0.6rem 1rem;
    --card-padding: 1rem;
}

*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    outline: 0;
    box-sizing: border-box;
    text-decoration: none;
    list-style: none;
    border: none;
}

body {
    font-family: "Poppins", sans-serif;
    color: var(--color-dark);
    background: var(--color-light);
    overflow-x: hidden;
}

/* GENERAL STYLES */
.container {
    width: 80%;
    margin: 0 auto;
}

.profile-photo {
    width: 3rem;
    position: relative;
}

.profile-photo img {
    display: block;
    width: 100%;
    border-radius: 50%;
}

.btn {
    display: inline-block;
    padding: var(--btn-padding);
    font-weight: 500;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 300ms ease;
    font-size: 0.9rem;
}

.btn:hover {
    opacity: 0.8;
}

.btn-primary {
    background: var(--color-primary);
    color: white;
}

.text-bold {
    font-weight: 500;
}

.text-muted {
    color: var(--color-gray);
}

/* ================= NAVBAR ================= */
nav {
    width: 100%;
    background: var(--color-white);
    padding: 0.7rem 0;
    position: fixed;
    top: 0;
    z-index: 10;
}

nav .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.search-bar {
    background: var(--color-light);
    border-radius: var(--border-radius);
    padding: var(--search-padding);
}

.search-bar input[type="search"] {
    background: transparent;
    margin-left: 1rem;
    font-size: 0.9rem;
    color: var(--color-dark);
}

nav .search-bar input[type="search"]::placeholder {
    color: var(--color-gray);
}

nav .create {
    display: flex;
    align-items: center;
    gap: 2rem;
}

/* ==================== MAIN ========================== */
main {
    position: relative;
    top: 5.4rem;
}

main .container {
    display: grid;
    grid-template-columns: 18vw auto 20vw;
    column-gap: 2rem;
    position: relative;
    overflow: hidden;
}

/* ==================== LEFT ========================== */
main .container .left {
    height: max-content;
    position: sticky;
    /* position: fixed; */
    top: var(--sticky-top-left);
}

main .container .left .profile {
    padding: var(--card-padding);
    background: var(--color-white);
    border-radius: var(--card-border-radius);
    display: flex;
    align-items: center;
    column-gap: 1rem;
    width: 100%;
}


/* ==================== SIDEBAR ========================== */
.left .sidebar {
    margin-top: 1rem;
    background: var(--color-white);
    border-radius: var(--card-border-radius);
}

.left .sidebar .menu-item {
    display: flex;
    align-items: center;
    height: 4rem;
    cursor: pointer;
    transition: all 300ms ease;
    position: relative;
}

.left .sidebar .menu-item:hover, .message:hover {
    background: var(--color-light);
}

.left .sidebar i {
    font-size: 1.4rem;
    color: var(--color-gray);
    margin-left: 2rem;
    position: relative;
}

.notification-count {
    background: var(--color-danger);
    color: white;
    font-size: 0.7rem;
    width: fit-content;
    border-radius: 0.8rem;
    padding: 0.1rem 0.4rem;
    position: absolute;
    top: -0.2rem;
    right: -0.3rem;
}

.left .sidebar h3 {
    margin-left: 1.5rem;
    font-size: 1rem;
}

.left .sidebar .active {
    background: var(--color-light);
}

.left .sidebar .active i,
.left .sidebar .active h3 {
    color: var(--color-primary);
}

.left .sidebar .active::before {
    content: "";
    display: block;
    width: 0.5rem;
    height: 100%;
    position: absolute;
    background: var(--color-primary);
}

.left .sidebar .menu-item:first-child.active {
    border-top-left-radius: var(--card-border-radius);
    overflow: hidden;
}

.left .sidebar .menu-item:last-child.active {
    border-bottom-left-radius: var(--card-border-radius);
    overflow: hidden;
}

.left .btn {
    margin-top: 1rem;
    width: 100%;
    text-align: center;
    padding: 1rem 0;
}


/*------------- NOTIFICATION POPUP ------------- */
.notifications-popup {
    box-shadow: 0 0 1rem var(--color-primary);
    position: absolute;
    top: 0;
    left: 110%;
    width: 30rem;
    max-height: 30rem;
    background: var(--color-white);
    border-radius: var(--card-border-radius);
    padding: var(--card-padding);
    box-shadow: 0 0 2rem hsl (var (--color-primary), 75%, 60% 25%);
    z-index: 8;
    display: none;
}

.notifications-popup::before {
    content: "";
    width: 1.2rem;
    height: 1.2rem;
    display: block;
    background: var(--color-white);
    position: absolute;
    left: -0.6rem;
    transform: rotate(45deg);
}


/* ==================== MIDDLE ========================== */
.middle{
    overflow-y: auto;
    height: 90vh;
    padding-right: 0.5rem;
}

.middle .stories {
    display: flex;
    justify-content: space-between;
    height: 12rem;
    gap: 0.5rem;
}

.middle .stories .story {
    padding: var(--card-padding);
    border-radius: var(--card-border-radius);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    color: white;
    font-size: 0.75rem;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.middle .stories .story::before {
    content: "";
    display: block;
    width: 100%;
    height: 5rem;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.75));
    position: absolute;
    bottom: 0;
}

.middle .stories .story .name {
    z-index: 2;
}

.middle .stories .story:nth-child(1) {
    background: url("./images/story-1.jpg") no-repeat center center/cover;
}

.middle .stories .story:nth-child(2) {
    background: url("./images/story-2.jpg") no-repeat center center/cover;
}

.middle .stories .story:nth-child(3) {
    background: url("./images/story-3.jpg") no-repeat center center/cover;
}

.middle .stories .story:nth-child(4) {
    background: url("./images/story-4.jpg") no-repeat center center/cover;
}

.middle .stories .story:nth-child(5) {
    background: url("./images/story-5.jpg") no-repeat center center/cover;
}

.middle .stories .story:nth-child(6) {
    background: url("./images/story-6.jpg") no-repeat center center/cover;
}

.middle .story .profile-photo {
    width: 2rem;
    height: 2rem;
    align-self: start;
    border: 3px solid var(--color-primary);
}


/*------------- CREATE POST ------------- */
.createPost {
    background: rgba(255, 255, 255, 0.5);
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
    text-align: center;
    display: grid;
    place-items: center;
    font-size: 0.9rem;
    display: none;
}


.create-post {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--color-white);
    padding: 0.4rem var(--card-padding);
    border-radius: var(--border-radius);
    box-shadow: 0 0 1rem var(--color-primary);
}


.create-post input[type="text"] {
    justify-self: start;
    width: 100%;
    padding-left: 1rem;
    background: transparent;
    color: var(--color-dark);
    margin-right: 1rem;
}

.extended-create-post {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
}

.post-fields {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    margin-top: 1rem;
    margin-bottom: 1rem;
}

.post-title,
.post-body,
.post-image {
    width: 80%;
    padding: 0.6rem 1rem;
    border-radius: var(--card-border-radius);
    border: 1px solid var(--color-light);
    font-family: "Poppins", sans-serif;
    font-size: 0.95rem;
    background: var(--color-light);
    color: var(--color-dark);
}

.post-body {
    resize: vertical;
    min-height: 80px;
    line-height: 1.5;
}

.post-image {
    background: none;
    color: var(--color-gray);
    border: none;
}

.post-categories {
    display: flex;
    flex-direction: column;
    gap: 0.4rem;
    font-size: 0.9rem;
    color: var(--color-dark);
    background: var(--color-light);
    padding: 0.6rem 1rem;
    border-radius: var(--card-border-radius);
    border: 1px solid var(--color-light);
}

.post-categories span {
    font-weight: 600;
    margin-bottom: 0.4rem;
    color: var(--color-black);
}

.post-categories label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.post-categories input[type="checkbox"] {
    accent-color: var(--color-primary);
}



/*------------- FEEDS ------------- */
.middle .feeds .feed {
    background: var(--color-white);
    border-radius: var(--card-border-radius);
    padding: var(--card-padding);
    margin: 1rem 0;
    font-size: 0.85rem;
    line-height: 1.5;
}

.middle .feed .head {
    display: flex;
    justify-content: space-between;
}

.middle .feed .user {
    display: flex;
    gap: 1rem;
}

.middle .feed .photo {
    border-radius: var(--card-border-radius);
    overflow: hidden;
    margin: 0.7rem 0;
}

.middle .feed .action-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.4rem;
    margin: 0.6rem;
}

.middle .liked-by {
    display: flex;
}

.middle .liked-by span {
    width: 1.4rem;
    height: 1.4rem;
    display: block;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid var(--color-white);
    margin-left: -0.6rem;
}



/* ==================== RIGHT ========================== */
main .container .right {
    height: max-content;
    position: sticky;
    top: var(--sticky-top-right);
    bottom: 0;
}

/*------------- MESSAGES ------------- */

.messages .heading {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.messages .body{
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    max-height: 20rem;
}

.quiteMsg {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 1rem;
}

.messages i {
    font-size: 1.4rem;
}

.messages .search-bar {
    display: flex;
    margin-bottom: 1rem;
}

.messages .category {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.messages .category h6 {
    width: 100%;
    text-align: center;
    border-bottom: 4px solid var(--color-light);
    padding-bottom: 0.5rem;
    font-size: 0.85rem;
}

.messages .category .active {
    border-color: var(--color-dark);
}

.messages .message {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    align-items: start;
    cursor: pointer;
}

.messages .message:last-child {
    margin: 0;
}

.messages .message p {
    font-size: 0.8rem;
}

.profile-photo .online {
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    border: 3px solid var(--color-white);
    background: var(--color-success);
    position: absolute;
    bottom: 0;
    right: 0;
}

.chat-view {
    /* transform: translateX(101%); */
    border-radius: 50%;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--color-white);
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
    z-index: 10;
  }
  
  .app.show-chat .chat-view {
    transform: translateX(0%);
  }
  
  .app.show-chat .messages {
    transform: translateX(-100%);
  }
  
  .chat-header {
    background: var(--color-primary);
    color: white;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .chat-header .back {
    font-size: 1.5rem;
    margin-right: 1rem;
    cursor: pointer;
    color: white;
  }
  
  .chat-messages {
    flex: 1;
    padding: 1rem;
    background: var(--color-light);
    overflow-y: auto;
    max-height: 20rem;
  }
  
  .chat-input {
    display: flex;
    padding: 1rem;
    border-top: 1px solid var(--color-light);
  }
  
  .chat-input input {
    flex: 1;
    padding: 0.8rem;
    border-radius: var(--card-border-radius);
    border: 1px solid var(--color-gray);
  }
  
  .chat-input button {
    padding: 0 1rem;
    margin-left: 0.5rem;
    background: var(--color-primary);
    color: white;
    border-radius: var(--card-border-radius);
    border: none;
  }
  
  /* Chat bubble */
  .bubble {
    max-width: 80%;
    min-width: 5rem;
    background: var(--color-secondary);
    padding: 0.6rem 1rem 1.2rem;
    margin-bottom: 0.5rem;
    border-radius: 1rem;
    position: relative;
    font-size: 0.95rem;
    line-height: 1.4;
  }

  .bubble.sent {
    background: var(--color-primary);
    color: white;
    align-self: flex-end;
    border-bottom-right-radius: 0.3rem;
  }
  
  .bubble.received {
    background: white;
    color: black;
    align-self: flex-start;
    border-bottom-left-radius: 0.3rem;
  }
  
  .chat-messages {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .bubble .msg-time {
    font-size: 0.7rem;
    color: black;
    position: absolute;
    bottom: 4px;
    right: 12px;
  }
  


/*------------- FRIEND REQUESTS ------------- */
.right .friend-requests {
    margin-top: 1rem;
}

.right .friend-requests h4 {
    color: var(--color-gray);
    margin: 1rem 0;
}

.right .request {
    background: var(--color-white);
    padding: var(--card-padding);
    border-radius: var(--card-border-radius);
    margin-bottom: 0.7rem;
}

.right .request .info {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.right .request .action {
    display: flex;
    gap: 1rem;
}


/* ==================== THEME CUSTOMIZATION ========================== */
.customize-theme {
    background: rgba(255, 255, 255, 0.5);
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
    text-align: center;
    display: grid;
    place-items: center;
    font-size: 0.9rem;
    display: none;
}

.customize-theme .card {
    background: var(--color-white);
    padding: 3rem;
    border-radius: var(--card-border-radius);
    width: 50%;
    box-shadow: 0 0 1rem var(--color-primary);
}

/*------------- FONT SIZE ------------- */
.customize-theme .font-size {
    margin-top: 5rem;
}

.customize-theme .font-size>div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--color-light);
    padding: var(--search-padding);
    border-radius: var(--card-border-radius);
}

.customize-theme .choose-size {
    background: var(--color-secondary);
    height: 0.3rem;
    width: 100%;
    margin: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.customize-theme .choose-size span {
    width: 1rem;
    height: 1rem;
    background: var(--color-secondary);
    border-radius: 50%;
    cursor: pointer;
}

.customize-theme .choose-size span.active {
    background: var(--color-primary);
}


/*------------- THEME COLORS ------------- */
.customize-theme .color {
    margin-top: 2rem;
}

.customize-theme .choose-color {
    background: var(--color-light);
    padding: var(--search-padding);
    border-radius: var(--card-border-radius);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.customize-theme .choose-color span {
    width: 2.2rem;
    height: 2.2rem;
    border-radius: 50%;
}

.customize-theme .choose-color span:nth-child(1) {
    background: hsl(252, 75%, 60%);
}

.customize-theme .choose-color span:nth-child(2) {
    background: hsl(52, 75%, 60%);
}

.customize-theme .choose-color span:nth-child(3) {
    background: hsl(352, 75%, 60%);
}

.customize-theme .choose-color span:nth-child(4) {
    background: hsl(152, 75%, 60%);
}

.customize-theme .choose-color span:nth-child(5) {
    background: hsl(202, 75%, 60%);
}

.customize-theme .choose-color span.active {
    border: 5px solid white;
}

/*------------- BACKGROUND COLORS ------------- */
.customize-theme .background {
    margin-top: 2rem;
}

.customize-theme .choose-bg {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1.5rem;
}

.customize-theme .choose-bg>div {
    padding: var(--card-padding);
    width: 100%;
    display: flex;
    align-items: center;
    font-size: 1rem;
    font-weight: bold;
    border-radius: 0.4rem;
    cursor: pointer;
}

.customize-theme .choose-bg>div.active {
    border: 2px solid var(--color-primary);
}

.customize-theme .choose-bg .bg-1 {
    background: white;
    color: black;
}

.customize-theme .choose-bg .bg-2 {
    background: hsl(252, 30%, 17%);
    color: white;
}

.customize-theme .choose-bg .bg-3 {
    background: hsl(252, 30%, 10%);
    color: white;
}

.customize-theme .choose-bg>div span {
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--color-gray);
    border-radius: 50%;
    margin-right: 1rem;
}

/* ==================== 
MEDIA QUERIES FOR SMALL LAPTOPS AND BIG TABLETS
========================== */
@media screen and (max-width: 1200px) {
    .container {
        width: 96%;
    }

    main .container {
        grid-template-columns: 5rem auto 30vw;
        gap: 1rem;
    }

    .left {
        width: 5rem;
        z-index: 5;
    }

    main .container .left .profile {
        display: none;
    }

    .sidebar h3 {
        display: none;
    }

    .left .btn {
        display: none;
    }

    .customize-theme .card{
        width: 80vw;
    }
}

/* ==================== 
MEDIA QUERIES FOR SMALL TABLETSTABLETSmessagemessage AND MOBILE PHONES
========================== */
@media screen and (max-width: 992px) {
    .create-post{
        width: 80%;
    }

    nav .search-bar {
        display: none;
    }

    main .container {
        grid-template-columns: 0 auto 0;
        gap: 0;
    }

    main .container .left {
        grid-column: 3/4;
        position: fixed;
        /* bottom: 0; */
        right: 0;
    }

    /*------------------------ NOTIFICATION POPUP ------------------------*/
    .notifications-popup {
        position: absolute;
        left: -20rem;
        width: 20rem;
    }

    .notifications-popup::before {
        display: absolute;
        top: 1.3rem;
        left: calc(20rem - 0.6rem);
        display: block;
    }

    main .container .middle {
        grid-column: 1/3;
    }

    main .container .sidebar{
        box-shadow: 0 0 1rem var(--color-primary);
    }

    main .container .right{
        display: none;
    }

    .customize-theme .card{
        width: 92vw;
    }
}