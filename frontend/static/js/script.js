// SIDEBAR
const menuItems = document.querySelectorAll('.menu-item');

// MESSAGES
const messages = document.querySelector('.messages');
const message = document.querySelectorAll('.message')
const messageSearch = document.querySelector('#message-search')
const chatView = document.querySelector('.chat-view');
const chatMessages = document.querySelector('.chat-messages');

// THEME
const theme = document.querySelector('#theme');
const themeModal = document.querySelector('.customize-theme');
const fontSizes = document.querySelectorAll('.choose-size span');
var root = document.querySelector(':root');
const colorPalette = document.querySelectorAll('.choose-color span');
const Bg1 = document.querySelector('.bg-1');
const Bg2 = document.querySelector('.bg-2');
const Bg3 = document.querySelector('.bg-3');


const createPost = document.querySelector('.createPost');

// ==================== SIDEBAR ==========================

// remove active class from all menu items
const changeActiveItem = () => {
    menuItems.forEach(item => {
        item.classList.remove('active');
    })
}

menuItems.forEach(item => {
    item.addEventListener('click', (e) => {
        changeActiveItem();
        item.classList.add('active');

        const notificationsPopup = document.querySelector('.notifications-popup');

        if (item.id === 'notifications') {
            if (!notificationsPopup.contains(e.target)) {
                const isVisible = notificationsPopup.style.display === 'block';
                notificationsPopup.style.display = isVisible ? 'none' : 'block';
            }
        } else {
            notificationsPopup.style.display = 'none';
        }
    });
});




// searches chats
const searchMessage = () => {
    const val = messageSearch.value.toLowerCase();
    message.forEach(user => {
        let name = user.querySelector('h5').textContent.toLocaleLowerCase();
        if (name.indexOf(val) != -1) {
            user.style.display = 'flex';
        } else {
            user.style.display = 'none';
        }
    })
}
// search chat
messageSearch.addEventListener('keyup', searchMessage);

// quite message
const quiteMsg = () => {
    document.querySelector('.notifications-popup').
        style.display = 'none';
}

// open chat
message.forEach(user => {
    user.addEventListener('click', () => {
        messages.style.display = 'none';
        chatView.style.display = 'flex';
        let name = user.querySelector('h5').textContent;
        let imgSrc = user.querySelector('img').src;
        openChat(name, imgSrc);
    })
})

function openChat(name, imgSrc) {
    document.getElementById('chatTitle').textContent = name;
    chatView.querySelector('#chatPhoto').src = imgSrc;
    // chatMessages.innerHTML = ''; // Clear previous chat
}

function goBack() {
    messages.style.display = 'block';
    chatView.style.display = 'none';
}

function getCurrentTime() {
    const now = new Date();
    let hours = now.getHours();
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12 || 12;
    return `${hours}:${minutes} ${ampm}`;
}

function sendMessage() {
    const input = document.getElementById('chatInput');
    const text = input.value.trim();
    if (text !== '') {
        const bubble = document.createElement('div');
        bubble.className = 'bubble sent'; // Add 'sent' class
        bubble.innerHTML = `
        ${text}
        <span class="msg-time">${getCurrentTime()}</span>
      `;
        document.getElementById('chatMessages').appendChild(bubble);
        input.value = '';
        document.getElementById('chatMessages').scrollTop = document.getElementById('chatMessages').scrollHeight;
    }
}

// CREATE POST
const openCreatePost = () => {
    createPost.style.display = 'block';
}

const closeCreatePost = (e) => {
    if (e.target.classList.contains('createPost')) {
        createPost.style.display = 'none';
    }
}

createPost.addEventListener('click', closeCreatePost);


// THEME/DISPLAY CUSTOMIZATION

// opens modal
const openThemeModal = () => {
    themeModal.style.display = 'grid';
}

// closes modal
const closeThemeModal = (e) => {
    if (e.target.classList.contains('customize-theme')) {
        themeModal.style.display = 'none';
    }
}

// close modal
themeModal.addEventListener('click', closeThemeModal);
theme.addEventListener('click', openThemeModal);


// ==================== FONTS ==========================
// remove active class from spans or font size selectors
const removeSizeSelector = () => {
    fontSizes.forEach(size => {
        size.classList.remove('active');
    })
}

fontSizes.forEach(size => {
    let fontSize;

    size.addEventListener('click', () => {
        removeSizeSelector();
        let fontSize;
        size.classList.add('active');

        if (size.classList.contains('font-size-1')) {
            fontSize = '10px';
            root.style.setProperty('---sticky-top-left', '5.4rem');
            root.style.setProperty('---sticky-top-right', '5.4rem');
        } else if (size.classList.contains('font-size-2')) {
            fontSize = '13px';
            root.style.setProperty('----sticky-top-left', '5.4rem');
            root.style.setProperty('----sticky-top-right', '-7rem');
        } else if (size.classList.contains('font-size-3')) {
            fontSize = '16px';
            root.style.setProperty('----sticky-top-left', '-2rem');
            root.style.setProperty('----sticky-top-right', '-17rem');
        } else if (size.classList.contains('font-size-4')) {
            fontSize = '19px';
            root.style.setProperty('----sticky-top-left', '-5rem');
            root.style.setProperty('----sticky-top-right', '-25rem');
        } else if (size.classList.contains('font-size-5')) {
            fontSize = '22px';
            root.style.setProperty('----sticky-top-left', '-12rem');
            root.style.setProperty('----sticky-top-right', '-35rem');
        }

        // change font size of the root html element
        document.querySelector('html').style.fontSize = fontSize;
    })
})

// remove active class from color spans
const removeActiveColor = () => {
    colorPalette.forEach(size => {
        size.classList.remove('active');
    })
}


// change primary colors
colorPalette.forEach(color => {
    color.addEventListener('click', () => {
        removeActiveColor();
        let primaryHue;
        if (color.classList.contains('color-1')) {
            primaryHue = 252;
        } else if (color.classList.contains('color-2')) {
            primaryHue = 52;
        } else if (color.classList.contains('color-3')) {
            primaryHue = 352;
        } else if (color.classList.contains('color-4')) {
            primaryHue = 152;
        } else if (color.classList.contains('color-5')) {
            primaryHue = 202;
        }
        color.classList.add('active');
        root.style.setProperty('--primary-color-hue', primaryHue);

    })
})


// theme BACKGROUND values
let lightColorLightness;
let whiteColorLightness;
let darkColorLightness;

// changes background color
const changeBG = () => {
    root.style.setProperty('--light-color-lightness', lightColorLightness);
    root.style.setProperty('--white-color-lightness', whiteColorLightness);
    root.style.setProperty('--dark-color-lightness', darkColorLightness);
}

// change background colors
Bg1.addEventListener('click', () => {
    darkColorLightness = '17%';
    whiteColorLightness = '100%';
    lightColorLightness = '95%';
    // add active class
    Bg1.classList.add('active');
    // remove active class from the others
    Bg2.classList.remove('active');
    Bg3.classList.remove('active');
    changeBG();
});

Bg2.addEventListener('click', () => {
    darkColorLightness = '95%';
    whiteColorLightness = '20%';
    lightColorLightness = '15%';
    // add active class
    Bg2.classList.add('active');
    // remove active class from the others
    Bg1.classList.remove('active');
    Bg3.classList.remove('active');
    changeBG();
});

Bg3.addEventListener('click', () => {
    darkColorLightness = '95%';
    whiteColorLightness = '10%';
    lightColorLightness = '0%';
    // add active class
    Bg3.classList.add('active');
    // remove active class from others
    Bg1.classList.remove('active');
    Bg2.classList.remove('active');
    changeBG();
})